import { ApiPropertyOptional } from '@nestjs/swagger';
import { TransformBoolean } from '@ghq-abi/northstar-api-libs';
import { IsBoolean, IsIn, IsOptional, IsString, IsEnum } from 'class-validator';
import { PaginatedRequest } from '../paginated.dto';
import { Transform } from 'class-transformer';
import { OrderBy, SortBy } from '@ghq-abi/northstar-domain';

export class GetDeliverablesRequestDto extends PaginatedRequest {
  @ApiPropertyOptional({
    description: 'Text search term to filter deliverables and projects by name',
    example: 'revenue',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Array of business function names to filter by',
    type: [String],
    isArray: true,
    example: ['Finance', 'Marketing', 'Operations'],
  })
  @IsOptional()
  businessFunctions?: string[];

  @ApiPropertyOptional({
    description:
      'Filter by active status. True for active items, false for inactive, omit for all',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(TransformBoolean)
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Deliverable Type Criteria',
    type: [String],
    enum: ['KPI', 'PROJECT', 'SCOPED_PROJECT_YES_NO'],
    isArray: true,
    example: ['PROJECT'],
  })
  @IsOptional()
  @IsIn(['KPI', 'PROJECT', 'SCOPED_PROJECT_YES_NO'], { each: true })
  deliverableTypes?: ('KPI' | 'PROJECT' | 'SCOPED_PROJECT_YES_NO')[];

  @ApiPropertyOptional({
    description: 'Field to sort by',
    enum: SortBy,
    example: SortBy.NAME,
  })
  @IsOptional()
  @IsEnum(SortBy)
  @Transform(({ value }) => (typeof value === 'string' ? value.toLowerCase() : value))
  sortBy?: SortBy;

  @ApiPropertyOptional({
    description: 'Sort direction',
    enum: ['asc', 'desc'],
    example: 'asc',
  })
  @IsOptional()
  @IsEnum(OrderBy)
  @Transform(({ value }) => (typeof value === 'string' ? value.toUpperCase() : value))
  orderBy?: OrderBy;
}
