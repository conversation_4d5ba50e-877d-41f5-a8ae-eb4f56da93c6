import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class OwnerDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  uid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiPropertyOptional()
  @IsString()
  positionTitle?: string;

  @ApiPropertyOptional({
    description: 'Full name of the owner',
    example: '<PERSON>',
  })
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Global identifier for the owner in the organization system',
    example: 12345,
  })
  @IsNumber()
  globalId: number;

  @ApiPropertyOptional({
    description: 'Email address of the owner',
    example: '<EMAIL>',
  })
  @IsString()
  email: string;
}
