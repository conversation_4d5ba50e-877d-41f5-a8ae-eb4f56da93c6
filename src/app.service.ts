import { Injectable, NotFoundException } from '@nestjs/common';
import {
  DeliverableRepository,
  DeliverableTypeRepository,
  DeliverableOwnerRepository,
  EmployeeEntity,
  EmployeeRepository,
  BusinessFunctionEntity,
  BusinessFunctionRepository,
} from '@ghq-abi/northstar-domain';
import { GetDeliverableResponseDto } from './dtos/responses/get-deliverable.dto';
import { GetDeliverablesResponseDto } from './dtos/responses/get-deliverables.dto';
import { CreateDeliverableRequestDto } from './dtos/requests/create-deliverable.dto';
import { UpdateDeliverableRequestDto } from './dtos/requests/update-deliverable.dto';
import { GetDeliverablesRequestDto } from './dtos/requests/get-deliverables.dto';
import { DeliverableDto } from './dtos/deliverable.dto';
import { GetEmployeesResponse } from './dtos/responses/get-employees.dto';
import { GetEmployeeResponse } from './dtos/responses/get-employee.dto';
import { GetEmployeesRequest } from './dtos/requests/get-employees.dto';
import { OwnerDto } from './dtos/owner.dto';
import { GetBusinessFunctionsResponse } from './dtos/responses/get-business-functions.dto';

@Injectable()
export class AppService {
  constructor(
    private readonly deliverableRepository: DeliverableRepository,
    private readonly deliverableTypeRepository: DeliverableTypeRepository,
    private readonly deliverableOwnerRepository: DeliverableOwnerRepository,
    private readonly employeeRepository: EmployeeRepository,
    private readonly businessFunctionRepository: BusinessFunctionRepository,
  ) { }

  private normalizeFilters(
    filters: GetDeliverablesRequestDto,
  ): GetDeliverablesRequestDto {
    if (!filters.businessFunctions) {
      return filters;
    }

    if (typeof filters.businessFunctions === 'string') {
      filters.businessFunctions = this.parseBusinessFunctions(
        filters.businessFunctions,
      );
    } else if (!Array.isArray(filters.businessFunctions)) {
      filters.businessFunctions = [String(filters.businessFunctions)];
    }

    return filters;
  }

  private parseBusinessFunctions(businessFunctions: string): string[] {
    try {
      const parsed = JSON.parse(businessFunctions);
      return Array.isArray(parsed)
        ? parsed.map(String)
        : [String(businessFunctions)];
    } catch {
      return [businessFunctions];
    }
  }

  getHealthCheck(): string {
    return 'OK';
  }

  private async ensureOwnersExist(
    owners: OwnerDto[],
    sessionUserUuid: string,
  ): Promise<void> {
    if (!owners?.length) {
      return;
    }

    const employeeUuids = owners.map((owner) => owner.uid);

    const existingOwners =
      await this.deliverableOwnerRepository.repository.find({
        where: employeeUuids
          .map((uuid) => [{ employeeUuid: uuid }, { uid: uuid }])
          .flat(),
      });

    const existingOwnerUuids = new Set(
      existingOwners.map((owner) => owner.uid),
    );

    const missingEmployeeUuids = employeeUuids.filter(
      (uuid) => !existingOwnerUuids.has(uuid),
    );

    if (!missingEmployeeUuids.length) {
      this.updateOwnerUids(owners, existingOwners);
      return;
    }

    const employees = await Promise.all(
      missingEmployeeUuids.map((uuid) =>
        this.employeeRepository.findById(uuid),
      ),
    );

    const validEmployees = employees.filter(Boolean);
    const foundEmployeeUuids = new Set(validEmployees.map((emp) => emp.uuid));

    const notFoundUuids = missingEmployeeUuids.filter(
      (uuid) => !foundEmployeeUuids.has(uuid),
    );

    if (notFoundUuids.length) {
      throw new NotFoundException(
        `Employee(s) with uid(s) ${notFoundUuids.join(', ')} not found.`,
      );
    }

    const newOwners = validEmployees.map((employee) =>
      this.deliverableOwnerRepository.repository.create({
        employeeUuid: employee.uuid,
        name: `${employee.firstname} ${employee.lastname}`.trim(),
        email: employee.email,
        globalId: employee.globalId,
        positionTitle: employee.positionTitle,
        createdBy: sessionUserUuid,
      }),
    );

    const savedOwners =
      await this.deliverableOwnerRepository.repository.save(newOwners);

    this.updateOwnerUids(owners, [...existingOwners, ...savedOwners]);
  }

  private updateOwnerUids(owners: OwnerDto[], deliverableOwners: any[]): void {
    const ownerMap = new Map(
      deliverableOwners.map((owner) => [owner.employeeUuid, owner.uid]),
    );

    owners.forEach((owner) => {
      const deliverableOwnerUid = ownerMap.get(owner.uid);
      if (deliverableOwnerUid) {
        owner.uid = deliverableOwnerUid;
      }
    });
  }

  async createDeliverableEntity(
    deliverableDto: CreateDeliverableRequestDto,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponseDto> {
    const validCreatedBy = sessionUserUuid || null;

    await this.processDeliverableOwners(deliverableDto.owners, validCreatedBy);

    const deliverablesEntities = await this.validateAndGetDeliverables(
      deliverableDto.deliverableUids,
    );

    const deliverableType = await this.validateAndGetDeliverableType(
      deliverableDto.type,
    );

    const scopedDeliverablesEntities = await this.processScopedDeliverables(
      deliverableDto.scopedDeliverables,
      sessionUserUuid,
    );

    const entity = this.createDeliverableEntityObject(
      deliverableDto,
      deliverableType,
      validCreatedBy,
    );

    const savedDeliverable =
      await this.deliverableRepository.repository.save(entity);

    await this.attachDeliverablesAndOwners(
      savedDeliverable,
      [...deliverablesEntities, ...scopedDeliverablesEntities],
      deliverableDto.owners,
    );

    return savedDeliverable;
  }

  private async processDeliverableOwners(
    owners: OwnerDto[],
    sessionUserUuid: string,
  ): Promise<void> {
    if (owners?.length) {
      await this.ensureOwnersExist(owners, sessionUserUuid);
    }
  }

  private async validateAndGetDeliverables(
    deliverableUids: string[],
  ): Promise<any[]> {
    if (!deliverableUids?.length) {
      return [];
    }

    const deliverablesEntities =
      await this.deliverableRepository.findByUids(deliverableUids);

    const foundUids = deliverablesEntities.map((d) => d.uid);
    const missingUids = deliverableUids.filter(
      (uid) => !foundUids.includes(uid),
    );

    if (missingUids.length) {
      throw new NotFoundException(
        `Deliverable(s) with uid(s) ${missingUids.join(', ')} not found.`,
      );
    }

    return deliverablesEntities;
  }

  private async validateAndGetDeliverableType(type: string): Promise<any> {
    if (!type) {
      return null;
    }

    const deliverableType =
      await this.deliverableTypeRepository.findByCode(type);

    if (!deliverableType) {
      throw new NotFoundException(
        `Deliverable type with code ${type} not found.`,
      );
    }

    return deliverableType;
  }

  private async processScopedDeliverables(
    scopedDeliverables: CreateDeliverableRequestDto[],
    sessionUserUuid: string,
  ): Promise<any[]> {
    if (!scopedDeliverables?.length) {
      return [];
    }

    return Promise.all(
      scopedDeliverables.map((scopedDeliverable) =>
        this.createDeliverableEntity(scopedDeliverable, sessionUserUuid),
      ),
    );
  }

  private createDeliverableEntityObject(
    deliverableDto: CreateDeliverableRequestDto,
    deliverableType: any,
    validCreatedBy: string,
  ): any {
    return this.deliverableRepository.repository.create({
      businessFunction: deliverableDto.businessFunction,
      calculationMethod: deliverableDto.calculationMethod,
      definition: deliverableDto.definition,
      frequency: deliverableDto.frequency,
      isActive: true,
      name: deliverableDto.name,
      buLevelAggregation: deliverableDto.buLevelAggregation,
      dateEnd: deliverableDto.dateEnd,
      dateStart: deliverableDto.dateStart,
      paValue: deliverableDto.paValue,
      dataSource: deliverableDto.dataSource,
      deliverableType,
      createdBy: validCreatedBy,
    });
  }

  private async attachDeliverablesAndOwners(
    savedDeliverable: any,
    deliverablesEntities: any[],
    owners: OwnerDto[],
  ): Promise<void> {
    if (deliverablesEntities.length) {
      savedDeliverable.deliverables = deliverablesEntities;
      await this.deliverableRepository.repository.save(savedDeliverable);
    }

    if (owners?.length) {
      await this.attachOwnersToDeliverable(savedDeliverable, owners);
    }
  }

  private async attachOwnersToDeliverable(
    savedDeliverable: any,
    owners: OwnerDto[],
  ): Promise<void> {
    const deliverableWithRelations =
      await this.deliverableRepository.repository.findOne({
        where: { uid: savedDeliverable.uid },
        relations: ['owners', 'deliverables', 'deliverableType'],
      });

    if (!deliverableWithRelations) {
      return;
    }

    const ownerEntities = await Promise.all(
      owners.map((owner) =>
        this.deliverableOwnerRepository.repository.findOne({
          where: { uid: owner.uid },
        }),
      ),
    );

    deliverableWithRelations.owners = ownerEntities.filter(Boolean);
    await this.deliverableRepository.repository.save(deliverableWithRelations);
  }

  async updateDeliverableEntity(
    uid: string,
    deliverableDto: UpdateDeliverableRequestDto,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponseDto> {
    const deliverable = await this.getExistingDeliverable(uid);

    await this.processDeliverableOwners(deliverableDto.owners, sessionUserUuid);

    const scopedDeliverablesEntities =
      await this.processScopedDeliverablesUpdate(
        deliverableDto.scopedDeliverables,
        sessionUserUuid,
      );

    const deliverablesEntities = await this.processDeliverableUidsUpdate(
      deliverable,
      deliverableDto.deliverableUids,
      scopedDeliverablesEntities,
    );

    await this.updateDeliverableFields(
      deliverable,
      deliverableDto,
      deliverablesEntities,
      sessionUserUuid,
    );

    const savedDeliverable =
      await this.deliverableRepository.repository.save(deliverable);

    return this.attachOwnersIfProvided(savedDeliverable, deliverableDto.owners);
  }

  private async getExistingDeliverable(uid: string): Promise<any> {
    const deliverable = await this.deliverableRepository.findByUid(uid);
    if (!deliverable) {
      throw new NotFoundException(`Deliverable with uid ${uid} not found.`);
    }
    return deliverable;
  }

  private async processScopedDeliverablesUpdate(
    scopedDeliverables: UpdateDeliverableRequestDto[],
    sessionUserUuid: string,
  ): Promise<any[]> {
    if (!scopedDeliverables?.length) {
      return [];
    }

    return Promise.all(
      scopedDeliverables.map((scopedDeliverable) =>
        this.createDeliverableEntity(
          scopedDeliverable as CreateDeliverableRequestDto,
          sessionUserUuid,
        ),
      ),
    );
  }

  private async processDeliverableUidsUpdate(
    deliverable: any,
    deliverableUids: string[],
    scopedDeliverablesEntities?: any[],
  ): Promise<any[]> {
    let baseDeliverables = deliverable.deliverables;

    if (deliverableUids !== undefined) {
      if (deliverableUids.length > 0) {
        baseDeliverables =
          await this.validateAndGetDeliverables(deliverableUids);
      } else {
        baseDeliverables = [];
      }
    }

    return [...baseDeliverables, ...(scopedDeliverablesEntities || [])];
  }

  private async updateDeliverableFields(
    deliverable: any,
    deliverableDto: UpdateDeliverableRequestDto,
    deliverablesEntities: any[],
    sessionUserUuid: string,
  ): Promise<void> {
    const { type, ...updateFields } = deliverableDto;

    Object.assign(deliverable, {
      ...updateFields,
      deliverables: deliverablesEntities,
      updatedBy: sessionUserUuid,
    });

    if (type !== undefined) {
      deliverable.deliverableType =
        await this.validateAndGetDeliverableType(type);
    }
  }

  private async attachOwnersIfProvided(
    savedDeliverable: any,
    owners: OwnerDto[],
  ): Promise<any> {
    if (!owners?.length) {
      return savedDeliverable;
    }

    const ownerEntities = await Promise.all(
      owners.map((owner) =>
        this.deliverableOwnerRepository.repository.findOne({
          where: { uid: owner.uid },
        }),
      ),
    );

    savedDeliverable.owners = ownerEntities.filter(Boolean);
    return this.deliverableRepository.repository.save(savedDeliverable);
  }

  async getDeliverableEntity(uid: string): Promise<GetDeliverableResponseDto> {
    const deliverable = await this.deliverableRepository.repository.findOne({
      where: { uid },
      relations: [
        'deliverableType',
        'deliverables',
        'deliverables.deliverableType',
      ],
    });

    if (!deliverable) {
      throw new NotFoundException(`Deliverable with uid ${uid} not found.`);
    }

    const { deliverableType, deliverables, ...deliverableData } =
      deliverable as any;
    return {
      ...deliverableData,
      type: deliverableType?.code,
      deliverables: deliverables.map((deliverable) => ({
        ...deliverable,
        type: deliverable.deliverableType?.code,
      })),
    } as any;
  }

  async getAllDeliverableEntities(
    filters: GetDeliverablesRequestDto,
  ): Promise<GetDeliverablesResponseDto> {
    const normalizedFilters = this.normalizeFilters(filters);
    const {
      search,
      businessFunctions,
      isActive,
      sortBy,
      orderBy,
      deliverableTypes,
      pageNumber,
      pageSize,
    } = normalizedFilters;

    const entities =
      await this.deliverableRepository.findCompactStandaloneWithFilters(
        search,
        businessFunctions,
        isActive,
        Array.isArray(deliverableTypes) ? deliverableTypes : [deliverableTypes],
        sortBy,
        orderBy,
        pageNumber,
        pageSize,
      );

    return {
      data: entities['data'] as DeliverableDto[],
      pageNumber: entities['pageNumber'],
      pageSize: entities['pageSize'],
      totalRecords: entities['totalRecords'],
    };
  }

  async createDeliverable(
    deliverableDto: CreateDeliverableRequestDto,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponseDto> {
    return this.createDeliverableEntity(deliverableDto, sessionUserUuid);
  }

  async updateDeliverable(
    uid: string,
    deliverableDto: UpdateDeliverableRequestDto,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponseDto> {
    return this.updateDeliverableEntity(uid, deliverableDto, sessionUserUuid);
  }

  async softDeleteDeliverable(
    uid: string,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponseDto> {
    const deliverable = await this.getExistingDeliverable(uid);
    await this.deliverableRepository.delete(uid, sessionUserUuid);
    return deliverable;
  }

  async getDeliverablesFunctions(): Promise<string[]> {
    const response = await this.deliverableRepository.getFunctions();
    return response?.length ? response.map((item) => item.function) : [];
  }

  async getEmployees(
    filters: GetEmployeesRequest,
  ): Promise<GetEmployeesResponse> {
    const { search } = filters;
    const isNumericSearch = this.isNumericString(search);

    const employees = isNumericSearch
      ? await this.getEmployeeByGlobalId(Number(search))
      : await this.getEmployeesByName(search);

    const mappedEmployees = this.mapEmployeesToResponse(employees);

    return {
      data: mappedEmployees,
      pageNumber: 1,
      pageSize: mappedEmployees.length,
      totalRecords: mappedEmployees.length,
    };
  }

  private isNumericString(value: string): boolean {
    return !isNaN(Number(value));
  }

  private async getEmployeeByGlobalId(
    globalId: number,
  ): Promise<EmployeeEntity[]> {
    const employee = await this.employeeRepository.findByGlobalId(globalId);
    return employee ? [employee] : [];
  }

  private async getEmployeesByName(name: string): Promise<EmployeeEntity[]> {
    return this.employeeRepository.findByName(name);
  }

  private mapEmployeesToResponse(employees: EmployeeEntity[]): any[] {
    return employees.map((emp) => ({
      uuid: emp.uuid,
      email: emp.email,
      firstName: emp.firstname,
      lastName: emp.lastname,
      positionTitle: emp.positionTitle,
      globalId: emp.globalId,
    }));
  }

  async getEmployee(uuid: string): Promise<GetEmployeeResponse> {
    const employee = await this.employeeRepository.findById(uuid);

    return {
      uuid: employee.uuid,
      email: employee.email,
      firstName: employee.firstname,
      lastName: employee.lastname,
      positionTitle: employee.positionTitle,
      globalId: employee.globalId,
    };
  }

  async getBusinessFunctions(): Promise<GetBusinessFunctionsResponse> {
    const items: BusinessFunctionEntity[] =
      await this.businessFunctionRepository.findAll();

    return {
      data: items.map((it) => ({ code: it.code, label: it.label })),
      pageNumber: 1,
      pageSize: items.length,
      totalRecords: items.length,
    };
  }

}
