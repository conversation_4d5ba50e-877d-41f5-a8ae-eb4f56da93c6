import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsBoolean,
  IsOptional,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { OwnerDto } from './owner.dto';

export class DeliverableResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the deliverable',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  uid: string;

  @ApiProperty({
    description: 'Name of the deliverable',
    example: 'Monthly Revenue Report',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Business function or department this deliverable belongs to',
    example: 'Finance',
  })
  @IsString()
  businessFunction: string;

  @ApiProperty({
    description: 'How often this deliverable is generated or updated',
    example: 'Monthly',
  })
  @IsString()
  frequency: string;

  @ApiProperty({
    description: 'Whether this deliverable is currently active and in use',
    example: true,
  })
  @IsBoolean()
  isActive: boolean;

  @ApiProperty({
    description: 'Method or formula used to calculate this deliverable',
    example: 'Sum of all revenue streams minus returns and discounts',
  })
  @IsString()
  calculationMethod: string;

  @ApiProperty({
    description: 'Detailed definition and purpose of this deliverable',
    example:
      'Monthly report showing total revenue generated across all business units',
  })
  @IsString()
  definition: string;

  @ApiProperty({
    description: 'Performance Analytics value or target for this deliverable',
    example: '$10M monthly target',
  })
  @IsString()
  paValue: string;

  @ApiProperty({
    description: 'Start date for this deliverable (formatted as YYYY)',
    type: String,
    example: '2024',
  })
  @IsString()
  dateStart: string;

  @ApiProperty({
    description: 'End date for this deliverable (formatted as YYYY)',
    type: String,
    example: '2024',
  })
  @IsString()
  dateEnd: string;

  @ApiPropertyOptional({
    description: 'How this deliverable is aggregated at business unit level',
    example: 'Sum across all BUs',
  })
  @IsOptional()
  @IsString()
  buLevelAggregation?: string;

  @ApiPropertyOptional({
    description: 'Deliverable type code',
    example: 'PROJECT',
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({
    description: 'Data source for this deliverable',
    example: 'Salesforce',
  })
  @IsOptional()
  @IsString()
  dataSource?: string;

  @ApiPropertyOptional({
    description: 'Associated deliverables',
    type: [DeliverableResponseDto],
    example: [],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DeliverableResponseDto)
  deliverables?: DeliverableResponseDto[];

  @ApiProperty({
    description: 'Date when the deliverable was created',
    type: Date,
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsString()
  createdAt: Date;

  @ApiPropertyOptional({
    description: 'Date when the deliverable was last updated',
    type: Date,
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsString()
  updatedAt?: Date;

  @ApiPropertyOptional({
    description: 'UUID of the user who created the deliverable',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiPropertyOptional({
    description: 'List of owners responsible for this deliverable',
    type: [OwnerDto],
    example: [
      { name: 'John Doe', email: '<EMAIL>', role: 'Data Analyst' },
    ],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OwnerDto)
  owners?: OwnerDto[];

  @ApiPropertyOptional({
    description: 'UUID of the user who last updated the deliverable',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsString()
  updatedBy?: string;
}
