/**
 * Utility functions for handling date processing in deliverables
 * Supports both YYYY format and full date format with backward compatibility
 */

/**
 * Checks if a string is in YYYY format (4 digits representing a year)
 */
export function isYearFormat(dateInput: string | Date): boolean {
  if (dateInput instanceof Date) {
    return false;
  }
  
  if (typeof dateInput !== 'string') {
    return false;
  }
  
  // Check if it's exactly 4 digits and represents a valid year
  const yearRegex = /^\d{4}$/;
  if (!yearRegex.test(dateInput)) {
    return false;
  }
  
  const year = parseInt(dateInput, 10);
  // Reasonable year range validation (1900-2100)
  return year >= 1900 && year <= 2100;
}

/**
 * Converts a YYYY string to a Date object representing January 1st of that year
 */
export function convertYearToStartDate(yearString: string): Date {
  if (!isYearFormat(yearString)) {
    throw new Error(`Invalid year format: ${yearString}. Expected YYYY format.`);
  }
  
  const year = parseInt(yearString, 10);
  return new Date(year, 0, 1); // January 1st of the year
}

/**
 * Converts a YYYY string to a Date object representing December 31st of that year
 */
export function convertYearToEndDate(yearString: string): Date {
  if (!isYearFormat(yearString)) {
    throw new Error(`Invalid year format: ${yearString}. Expected YYYY format.`);
  }
  
  const year = parseInt(yearString, 10);
  return new Date(year, 11, 31, 23, 59, 59, 999); // December 31st, 23:59:59.999
}

/**
 * Processes dateStart input - converts YYYY to Date or returns existing Date
 */
export function processDateStart(dateInput: string | Date): Date {
  if (dateInput instanceof Date) {
    return dateInput;
  }
  
  if (typeof dateInput === 'string') {
    if (isYearFormat(dateInput)) {
      return convertYearToStartDate(dateInput);
    }
    // If it's not YYYY format, try to parse as regular date
    const parsedDate = new Date(dateInput);
    if (isNaN(parsedDate.getTime())) {
      throw new Error(`Invalid date format: ${dateInput}`);
    }
    return parsedDate;
  }
  
  throw new Error(`Invalid dateStart input: ${dateInput}`);
}

/**
 * Processes dateEnd input - returns Date if provided, or generates end date if dateStart is YYYY
 */
export function processDateEnd(
  dateEndInput: string | Date | undefined,
  dateStartInput: string | Date
): Date | undefined {
  // If dateEnd is explicitly provided, process it normally
  if (dateEndInput !== undefined) {
    if (dateEndInput instanceof Date) {
      return dateEndInput;
    }
    
    if (typeof dateEndInput === 'string') {
      if (isYearFormat(dateEndInput)) {
        return convertYearToEndDate(dateEndInput);
      }
      // If it's not YYYY format, try to parse as regular date
      const parsedDate = new Date(dateEndInput);
      if (isNaN(parsedDate.getTime())) {
        throw new Error(`Invalid date format: ${dateEndInput}`);
      }
      return parsedDate;
    }
  }
  
  // If dateEnd is not provided but dateStart is in YYYY format, auto-generate dateEnd
  if (typeof dateStartInput === 'string' && isYearFormat(dateStartInput)) {
    return convertYearToEndDate(dateStartInput);
  }
  
  // Otherwise, return undefined (dateEnd is optional)
  return undefined;
}

/**
 * Formats a Date object to YYYY string for response
 */
export function formatDateToYear(date: Date): string {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    throw new Error('Invalid date provided for formatting');
  }
  
  return date.getFullYear().toString();
}

/**
 * Processes deliverable dates for creation/update operations
 */
export interface ProcessedDates {
  dateStart: Date;
  dateEnd?: Date;
}

export function processDeliverableDates(
  dateStart: string | Date,
  dateEnd?: string | Date
): ProcessedDates {
  const processedDateStart = processDateStart(dateStart);
  const processedDateEnd = processDateEnd(dateEnd, dateStart);
  
  return {
    dateStart: processedDateStart,
    dateEnd: processedDateEnd,
  };
}

/**
 * Formats deliverable dates for response (converts to YYYY format)
 */
export interface FormattedDates {
  dateStart: string;
  dateEnd: string;
}

export function formatDeliverableDatesForResponse(
  dateStart: Date,
  dateEnd: Date
): FormattedDates {
  return {
    dateStart: formatDateToYear(dateStart),
    dateEnd: formatDateToYear(dateEnd),
  };
}
