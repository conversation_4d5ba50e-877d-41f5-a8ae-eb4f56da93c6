import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { Public, User, UserSession } from '@ghq-abi/northstar-api-libs';
import { v4 as uuidv4 } from 'uuid';
import { AppService } from './app.service';
import { GetBusinessFunctionsResponse } from './dtos/responses/get-business-functions.dto';
import { GetDeliverableResponseDto } from './dtos/responses/get-deliverable.dto';
import { GetDeliverablesResponseDto } from './dtos/responses/get-deliverables.dto';
import { CreateDeliverableRequestDto } from './dtos/requests/create-deliverable.dto';
import { UpdateDeliverableRequestDto } from './dtos/requests/update-deliverable.dto';
import { GetDeliverablesRequestDto } from './dtos/requests/get-deliverables.dto';
import { GetEmployeesResponse } from './dtos/responses/get-employees.dto';
import { GetEmployeesRequest } from './dtos/requests/get-employees.dto';
import { GetEmployeeResponse } from './dtos/responses/get-employee.dto';

@ApiTags('API')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) { }

  @Get('health-check')
  @Public()
  @ApiOperation({
    summary: 'Health Check',
    description: 'Returns the health status of the KPI Catalog API service',
  })
  @ApiResponse({
    status: 200,
    description: 'Service is healthy',
    schema: {
      type: 'string',
      example: 'OK',
    },
  })
  getHealthCheck(): string {
    return this.appService.getHealthCheck();
  }

  @Get('deliverables')
  @ApiOperation({
    summary: 'Get All Deliverables',
    description:
      'Retrieves a complete list of all deliverables in the catalog with full details including metadata, owners, and relationships',
  })
  @ApiQuery({
    name: 'search',
    description: 'Text search term to filter by name',
    required: false,
    type: 'string',
    example: 'revenue',
  })
  @ApiQuery({
    name: 'businessFunctions',
    description: 'Array of function names to filter by',
    required: false,
    type: 'array',
    items: { type: 'string' },
    example: ['Finance', 'Marketing'],
  })
  @ApiQuery({
    name: 'isActive',
    description: 'Filter by active status',
    required: false,
    type: 'boolean',
    example: true,
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'Field to sort by',
    required: false,
    type: 'string',
    enum: ['name', 'usage'],
    example: 'name',
  })
  @ApiQuery({
    name: 'orderBy',
    description: 'Sort direction',
    required: false,
    type: 'string',
    enum: ['asc', 'desc'],
    example: 'asc',
  })
  @ApiQuery({
    name: 'deliverableTypes',
    description: 'Deliverable Type Criteria',
    required: false,
    type: 'array',
    items: {
      type: 'string',
      enum: ['KPI', 'PROJECT', 'SCOPED_PROJECT_YES_NO'],
    },
    example: ['PROJECT'],
  })
  @ApiQuery({
    name: 'pageNumber',
    description: 'Page number for pagination',
    required: true,
    type: 'number',
    example: 1,
  })
  @ApiQuery({
    name: 'pageSize',
    description: 'Number of items per page',
    required: true,
    type: 'number',
    example: 10,
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved all deliverables',
    type: GetDeliverablesResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Deliverable not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getAllDeliverables(
    @Query() filters: GetDeliverablesRequestDto,
  ): Promise<GetDeliverablesResponseDto> {
    return this.appService.getAllDeliverableEntities(filters);
  }

  @Get('deliverables/:uid')
  @ApiOperation({
    summary: 'Get Deliverable by UID',
    description: 'Retrieves a specific deliverable by its unique identifier',
  })
  @ApiParam({
    name: 'uid',
    description: 'Unique identifier of the deliverable to retrieve',
    type: 'string',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved deliverable',
    type: GetDeliverableResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Deliverable not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getDeliverableByUid(
    @Param('uid') uid: string,
  ): Promise<GetDeliverableResponseDto> {
    return this.appService.getDeliverableEntity(uid);
  }

  @Post('deliverables')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create Deliverable',
    description:
      'Creates a new deliverable in the catalog with the provided details. The createdBy field will be automatically populated with the UUID of the authenticated user.',
  })
  @ApiBody({
    type: CreateDeliverableRequestDto,
    description: 'Deliverable creation data',
  })
  @ApiResponse({
    status: 201,
    description: 'Deliverable successfully created',
    type: GetDeliverableResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async createDeliverable(
    @Body() deliverableDto: CreateDeliverableRequestDto,
    @User() user: UserSession,
  ): Promise<GetDeliverableResponseDto> {
    console.log('User session in controller:', user);
    console.log('User UUID:', user?.uuid);

    // Provide a fallback UUID for development/testing if user session is not available
    const userUuid = user?.uuid || uuidv4();
    console.log('Using userUuid:', userUuid);

    return this.appService.createDeliverableEntity(deliverableDto, userUuid);
  }

  @Put('deliverables/:uid')
  @ApiOperation({
    summary: 'Update Deliverable',
    description:
      'Updates an existing deliverable with the provided data. The updatedBy field will be automatically populated with the UUID of the authenticated user.',
  })
  @ApiParam({
    name: 'uid',
    description: 'Unique identifier of the deliverable to update',
    type: 'string',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({
    type: UpdateDeliverableRequestDto,
    description: 'Deliverable update data',
  })
  @ApiResponse({
    status: 200,
    description: 'Deliverable successfully updated',
    type: GetDeliverableResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 404,
    description: 'Deliverable not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async updateDeliverable(
    @Param('uid') uid: string,
    @Body() deliverableDto: UpdateDeliverableRequestDto,
    @User() user: UserSession,
  ): Promise<GetDeliverableResponseDto> {
    const userUuid = user?.uuid || uuidv4();
    return this.appService.updateDeliverableEntity(
      uid,
      deliverableDto,
      userUuid,
    );
  }

  @Delete('deliverables/:uid')
  @ApiOperation({
    summary: 'Soft Delete Deliverable',
    description:
      'Performs a soft delete on a deliverable (marks as deleted without removing from database)',
  })
  @ApiParam({
    name: 'uid',
    description: 'Unique identifier of the deliverable to delete',
    type: 'string',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Deliverable successfully soft deleted',
    type: GetDeliverableResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Deliverable not found',
  })
  @ApiResponse({
    status: 501,
    description: 'Not implemented yet',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async softDeleteDeliverable(
    @Param('uid') uid: string,
    @User() user: UserSession,
  ): Promise<GetDeliverableResponseDto> {
    const userUuid = user?.uuid || uuidv4();
    return this.appService.softDeleteDeliverable(uid, userUuid);
  }

  @Get('employees')
  async getEmployees(
    @Query() filters: GetEmployeesRequest,
  ): Promise<GetEmployeesResponse> {
    return await this.appService.getEmployees(filters);
  }

  @Get('employees/:uuid')
  async getEmployee(@Param('uuid') uuid: string): Promise<GetEmployeeResponse> {
    return await this.appService.getEmployee(uuid);
  }

  @Get('business-functions')
  async getBusinessFunctions(): Promise<GetBusinessFunctionsResponse> {
    return await this.appService.getBusinessFunctions();
  }

}

