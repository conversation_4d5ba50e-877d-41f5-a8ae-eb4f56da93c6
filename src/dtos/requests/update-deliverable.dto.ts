import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsBoolean,
  IsDate,
  IsOptional,
  IsArray,
} from 'class-validator';
import { Type } from 'class-transformer';
import { OwnerDto } from '../owner.dto';

export class UpdateDeliverableRequestDto {
  @ApiPropertyOptional({
    description: 'Name of the deliverable',
    example: 'Monthly Revenue Report',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Business function or department this deliverable belongs to',
    example: 'Finance',
  })
  @IsOptional()
  @IsString()
  businessFunction?: string;

  @ApiPropertyOptional({
    description: 'How often this deliverable is generated or updated',
    example: 'Monthly',
  })
  @IsOptional()
  @IsString()
  frequency?: string;

  @ApiPropertyOptional({
    description: 'Whether this deliverable is currently active and in use',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Method or formula used to calculate this deliverable',
    example: 'Sum of all revenue streams minus returns and discounts',
  })
  @IsOptional()
  @IsString()
  calculationMethod?: string;

  @ApiPropertyOptional({
    description: 'Detailed definition and purpose of this deliverable',
    example:
      'Monthly report showing total revenue generated across all business units',
  })
  @IsOptional()
  @IsString()
  definition?: string;

  @ApiPropertyOptional({
    description: 'Performance Analytics value or target for this deliverable',
    example: '$10M monthly target',
  })
  @IsOptional()
  @IsString()
  paValue?: string;

  @ApiPropertyOptional({
    description: 'Start date for this deliverable',
    type: Date,
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  dateStart?: Date;

  @ApiPropertyOptional({
    description: 'End date for this deliverable',
    type: Date,
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  dateEnd?: Date;

  @ApiPropertyOptional({
    description: 'How this deliverable is aggregated at business unit level',
    example: 'Sum across all BUs',
  })
  @IsOptional()
  @IsString()
  buLevelAggregation?: string;

  @ApiPropertyOptional({
    description: 'Code of the deliverable type this deliverable belongs to',
    example: 'KPI',
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({
    description:
      'Array of deliverable UIDs that are associated with this deliverable',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-************',
      '987fcdeb-51a2-43d1-9f4e-123456789abc',
    ],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  deliverableUids?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  scopedDeliverables?: UpdateDeliverableRequestDto[];

  @ApiPropertyOptional({
    description:
      'Array of deliverable UIDs that are associated with this deliverable',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-************',
      '987fcdeb-51a2-43d1-9f4e-123456789abc',
    ],
  })
  @IsOptional()
  @IsArray()
  owners?: OwnerDto[];
}
